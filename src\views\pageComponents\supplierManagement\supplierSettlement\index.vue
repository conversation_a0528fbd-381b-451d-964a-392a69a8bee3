<template>
  <div class="main">
    <div class="mask" v-if="isShowLoadingMask">
      <a-spin />
    </div>
    <div class="mask" v-if="isShowMask">
      <!-- 协议弹窗 -->
      <AgreementModal v-model:visible="isShowMask" @confirm="handleAgreementConfirm" />
    </div>
    <a-steps :current="step" :items="stepList" labelPlacement="vertical" status="process"></a-steps>
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :colon="false" :style="isShowLoadingMask || isShowMask ? 'overflow: hidden' : ''">
      <section v-show="step == 0">
        <div class="title">公司基本信息</div>
        <a-form-item label="营业执照" name="fileList">
          <div class="flex">
            <a-upload
              style="width: 100px"
              v-model:file-list="fileList"
              list-type="picture-card"
              :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, fileList, 1)"
              @preview="handlePreview"
              :max-count="1"
            >
              <div v-if="fileList.length == 0">
                <PlusOutlined />
              </div>
            </a-upload>
            <span class="text-xs text-500 text-gray">
              1.支持 jpg/jpeg/png 格式
              <br />
              2.文件大小不得大于10M
            </span>
          </div>
        </a-form-item>
        <a-form-item label="证件编号" name="credit_id">
          <a-input v-model:value="form.credit_id" class="input-width" placeholder="请输入证件编号"></a-input>
        </a-form-item>
        <a-form-item label="统一社会信用代码" name="credit_code">
          <a-input v-model:value="form.credit_code" class="input-width" placeholder="请输入统一社会信用代码"></a-input>
          <a href="https://www.gsxt.gov.cn/index.html" target="_blank">（可在企信网上查询得到，去查询）</a>
        </a-form-item>
        <a-form-item label="公司名称" name="supplier_name">
          <a-input v-model:value="form.supplier_name" class="input-width" placeholder="请输入公司名称" :disabled="userData.company_id"></a-input>
          <!-- <template v-if="userData.company">
            <a-button type="link" @click="onClickCreateCompany" style="padding: 8px">新增入驻企业</a-button>
            <a-tooltip placement="rightTop">
              <template #title>
                <span>增加一个新增账号数据，审核通过后，该账号与入驻企业直接绑定。</span>
              </template>
              <QuestionCircleFilled style="font-size: 18px; color: gray" />
            </a-tooltip>
          </template> -->
        </a-form-item>
        <a-form-item label="公司类型" name="company_type">
          <a-select
            class="input-width"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="form.company_type"
            placeholder="请选择公司类型"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="(item, i) in companyTypeOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="公司规模" name="business_scale">
          <a-select
            class="input-width"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="form.business_scale"
            placeholder="请选择公司规模"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="(item, i) in companyModeOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="成立日期" name="establishment_date">
          <a-date-picker v-model:value="form.establishment_date" valueFormat="YYYY-MM-DD" class="input-width" />
        </a-form-item>
        <a-form-item label="营业执照有效期" name="business_license_validity">
          <a-space>
            <a-date-picker v-model:value="form.business_license_validity" valueFormat="YYYY-MM-DD" class="input-width" :disabled="form.is_long" />
            <a-checkbox v-model:checked="form.is_long">长期</a-checkbox>
          </a-space>
        </a-form-item>
        <a-form-item label="营业执照地址" name="business_license_address">
          <a-space>
            <a-cascader class="short" v-model:value="form.bussinessAddress" :options="areaOptions" placeholder="请选择省市区" />
            <a-input v-model:value="form.business_license_address" class="input-width" placeholder="详细地址" :maxlength="200"></a-input>
          </a-space>
        </a-form-item>
        <a-form-item label="办公地址" name="office_address_detail">
          <a-space>
            <a-cascader
              class="short"
              v-model:value="form.officeAddress"
              :options="areaOptions"
              placeholder="请选择省市区"
              @change="
                () => {
                  if (!form.officeAddress?.length) {
                    form.office_address_detail = ''
                  }
                }
              "
            />
            <a-input v-model:value="form.office_address_detail" class="input-width" placeholder="详细地址" @change="changeOfficeAddressDetail" :maxlength="200"></a-input>
          </a-space>
        </a-form-item>
        <div class="title">法定代表人信息</div>
        <a-form-item label="法人姓名" name="legal_person_name">
          <a-input v-model:value="form.legal_person_name" class="input-width" placeholder="请输入法人姓名"></a-input>
        </a-form-item>
        <a-form-item label="证件类型" name="certificate_type">
          <a-select
            class="input-width"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="form.certificate_type"
            placeholder="请选择证件类型"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="(item, i) in documentTypeOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="证件号" name="certificate_number">
          <a-input v-model:value="form.certificate_number" class="input-width" placeholder="请输入证件号" :controls="false"></a-input>
        </a-form-item>
        <a-form-item label="证件照" name="photoList">
          <a-space class="items-start text-gray">
            <a-upload
              v-model:file-list="photoList"
              list-type="picture-card"
              :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, photoList, 2)"
              @preview="handlePreview"
              :max-count="1"
            >
              <div v-if="photoList.length == 0">
                <PlusOutlined />
                <div>正面</div>
              </div>
            </a-upload>
            <a-upload
              v-model:file-list="backPhotoList"
              list-type="picture-card"
              :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, backPhotoList, 3)"
              @preview="handlePreview"
              :max-count="1"
            >
              <div v-if="backPhotoList.length == 0">
                <PlusOutlined />
                <div>反面</div>
              </div>
            </a-upload>
            <span class="text-xs text-500">
              1.支持 jpeg/png 格式
              <br />
              2.文件大小不得大于10M
            </span>
          </a-space>
        </a-form-item>
      </section>
      <section v-show="step == 1">
        <div class="title">公司扩展信息</div>
        <a-form-item label="供应商类型" name="supplier_type">
          <a-select
            class="input-width"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="form.supplier_type"
            placeholder="请选择公司类型"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="(item, i) in supplierTypeOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="自营/合作工厂规模" name="factory_scale">
          <a-radio-group v-model:value="form.factory_scale" name="radioGroup">
            <a-radio v-for="(item, i) in factoryscaleOptions" :key="i" :value="item.value">{{ item.label }}</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="主营类目（多选）" name="main_categories">
          <a-checkbox-group v-model:value="form.main_categories" name="majorTypegroup" :options="majorTypeOptions" />
        </a-form-item>
        <a-form-item label="主营区域（多选）" name="main_regions">
          <a-checkbox-group v-model:value="form.main_regions" name="majorAreagroup" :options="majorAreaOptions" />
        </a-form-item>
        <a-form-item label="工厂人员规模">
          <a-input-number v-model:value="form.factory_employee_count" addon-after="人" :precision="0" :max="99999"></a-input-number>
        </a-form-item>
        <a-form-item label="SKU数量">
          <a-input-number v-model:value="form.sku_count" addon-after="个" :precision="0" :max="999999"></a-input-number>
        </a-form-item>
        <a-form-item label="主营商品" name="main_products">
          <a-input v-model:value="form.main_products" class="input-width" placeholder="请输入贵司主营商品，存在多个用逗号隔开"></a-input>
        </a-form-item>
        <a-form-item label="主营商品价格区间" name="main_products_min_price">
          <a-input-number class="price-before" v-model:value="form.main_products_min_price" addon-after="-" :precision="2" :max="9999999.99"></a-input-number>
          <a-input-number class="price-affter" v-model:value="form.main_products_max_price" addon-after="元" :precision="2" :max="9999999.99"></a-input-number>
        </a-form-item>
        <a-form-item label="公司年销售额" name="annual_sales">
          <a-input-number class="input-width" v-model:value="form.annual_sales" addon-after="万元" :precision="2" :max="9999999.99"></a-input-number>
        </a-form-item>
      </section>
      <section v-if="step == 2">
        <div class="title">
          联系人信息
          <span class="grey">（最多支持添加5个）</span>
        </div>
        <div class="vxe-table-box">
          <vxe-table class="rule-table" ref="contactsTableRef" keep-source border show-overflow height="288" :data="form.srs_supplier_contact_infos">
            <vxe-column field="name" title="联系人姓名">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'name']" :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 20 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.name"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="job" title="职务">
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'job']" :rules="[{ validator: () => validators({ row, column, length: 20 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.job"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="mobile_phone_number" title="手机号">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_contact_infos', 'mobile_phone_number']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 13 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input-number class="w100" v-model:value="row.mobile_phone_number"></a-input-number>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="email" title="邮箱">
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'email']" :rules="[{ validator: () => validateEmail({ row, column, length: 50 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.email"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="weixin_number" title="微信">
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'weixin_number']" :rules="[{ validator: () => validators({ row, column, length: 20 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.weixin_number"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="is_default" title="默认联系人">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column, rowIndex }">
                <a-form-item
                  :name="['srs_supplier_contact_infos', 'is_default']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]"
                >
                  <a-select
                    class="w100"
                    v-model:value="row.is_default"
                    :options="trueOrFalseOptions"
                    :filter-option="filterOption"
                    @change="changeContactDefault($event, rowIndex, 'srs_supplier_contact_infos')"
                  />
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="operate" title="操作">
              <template #default="{ rowIndex }">
                <a-space>
                  <MinusOutlined style="color: red" @click="onDelContact(rowIndex, 'srs_supplier_contact_infos')" />
                  <PlusOutlined v-if="rowIndex >= 1 || rowIndex == form.srs_supplier_contact_infos.length - 1" @click="onAddTableItem('srs_supplier_contact_infos')" />
                </a-space>
              </template>
            </vxe-column>
          </vxe-table>
        </div>

        <div class="title">
          财务信息
          <span class="grey">（最多支持添加5个）</span>
        </div>
        <a-row class="pl-20 pr-20">
          <a-col :span="8">
            <a-form-item label="结算方式" name="settlement_method">
              <a-select v-model:value="form.settlement_method" :options="settlementMethodOptions" :filter-option="filterOption"></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="发票类型" name="invoice_type">
              <a-select v-model:value="form.invoice_type" :options="invoiceTypeOptions" :filter-option="filterOption"></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="默认税率" name="default_tax_rate">
              <a-select v-model:value="form.default_tax_rate" :options="defaultTaxRateOptions" :filter-option="filterOption"></a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="vxe-table-box">
          <vxe-table class="rule-table" ref="financeTableRef" keep-source border show-overflow height="288" :data="form.srs_supplier_finance_infos">
            <vxe-column field="account_name" title="账户名称">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'account_name']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.account_name"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="account_type" title="账户类型" :edit-render="{ name: 'VxeSelect' }">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'account_type']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]"
                >
                  <a-select class="w100" v-model:value="row.account_type" :options="accountTypeOptions" :filter-option="filterOption" />
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="collection_card_number" title="银行卡卡号">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'collection_card_number']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.collection_card_number" onkeyup="value=value.replace(/[^\d]/g,'')"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="collection_bank" title="开户行">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'collection_bank']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.collection_bank"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="collection_bank_branch" title="所属支行">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'collection_bank_branch']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.collection_bank_branch"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="is_default" title="是否默认" :edit-render="{ name: 'VxeSelect' }">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column, rowIndex }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'is_default']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]"
                >
                  <a-select
                    class="w100"
                    v-model:value="row.is_default"
                    :options="trueOrFalseOptions"
                    :filter-option="filterOption"
                    @change="changeContactDefault($event, rowIndex, 'srs_supplier_finance_infos')"
                  />
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="operate" title="操作">
              <template #default="{ rowIndex }">
                <a-space>
                  <MinusOutlined style="color: red" @click="onDelContact(rowIndex, 'srs_supplier_finance_infos')" />
                  <PlusOutlined v-if="rowIndex >= 1 || rowIndex == form.srs_supplier_finance_infos.length - 1" @click="onAddTableItem('srs_supplier_finance_infos')" />
                </a-space>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </section>
      <section v-show="step == 3">
        <div class="title">上传文件</div>
        <a-form-item name="uploadFileList" label="上传文件">
          <div class="uploadBox">
            <a-upload v-model:file-list="uploadFileList" :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, certificateFileList, 4)" :max-count="50" :showUploadList="false">
              <a-button>上传文件</a-button>
            </a-upload>
            <div class="uploadBoxtext">
              1. 支持文件格式：PNG，JPEG，JPG，PDF等；
              <br />
              2. 限制单个文件 ≤ 10M ；
            </div>
          </div>
          <div class="richtextbox" v-html="uploadFilesTips"></div>
          <a-table :columns="columns" :data-source="certificateFileList" class="w-90% mt20 mb20" bordered size="small" :pagination="false">
            <template #bodyCell="{ column, rowIndex, record }">
              <template v-if="column.dataIndex === 'operation'">
                <a-space>
                  <a @click="ToPagePreviewbyId(record.id)">预览</a>
                  <a @click="onDelFile(rowIndex)">删除</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-form-item>
      </section>
      <section v-show="step == 4" class="pd20">
        <div v-if="[20, 30].includes(form.audit_status) && !form.company_id">
          <p class="strong font-xl text-center color333">入驻申请已完成，请等待审核</p>
          <CheckCircleFilled class="icon origin center mb30" />
          <p class="strong text-center font-l">提交成功！</p>
          <p class="text-center">感谢您提交入驻申请，我们将尽快完成审核流程。</p>
        </div>
        <div v-else-if="form.company_id">
          <CheckCircleFilled class="icon green center mb30" />
          <p class="strong text-center font-l">审核通过！</p>
          <p class="text-center">恭喜您，入驻成功！</p>
        </div>
        <div v-else-if="form.audit_status == 95 && !form.company_id">
          <CloseCircleFilled class="icon center mb30 red" />
          <p class="strong text-center font-l">审核不通过</p>
          <p class="red text-center">失败原因：{{ form.audit_opinion }}</p>
        </div>
        <div class="apply-info center">
          <div class="w540">
            <p class="font-l mt20">申请信息摘要</p>
            <a-row>
              <a-col :span="12" class="mr-60">公司名称 {{ form.supplier_name }}</a-col>
              <a-col :span="8">公司类型 {{ form.company_type_string }}</a-col>
            </a-row>
            <a-row>
              <a-col :span="12" class="mr-60">联系人 {{ form?.srs_supplier_contact_infos?.find((n) => n.is_default)?.name }}</a-col>
              <a-col :span="8">联系方式 {{ form?.srs_supplier_contact_infos?.find((n) => n.is_default)?.mobile_phone_number }}</a-col>
            </a-row>
            <a-row>
              <a-col :span="12">申请时间 {{ form.application_at }}</a-col>
            </a-row>
          </div>
        </div>
        <div class="text-center mt30">
          <a-button v-if="form.company_id && checkPagePermission('/companyInfo')" type="primary" @click="onJumpDetail">前往【企业资料】</a-button>
          <a-button v-else-if="form.audit_status == 95 && !form.company_id" type="primary" @click="onJumpFirst">返回完善数据</a-button>
        </div>
      </section>

      <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
        <a-space>
          <a-button v-if="step > 0 && step < 4" type="primary" @click="onClickBack" :loading="loading">上一步</a-button>
          <a-button v-if="step < 3" type="primary" @click="onSubmit" :loading="loading">下一步</a-button>
          <a-button v-if="step == 3" type="primary" @click="onSubmit(1)" :loading="loading">提交</a-button>
          <a-button v-if="step < 4" @click="onSaveDraft" :loading="loading">保存草稿</a-button>
        </a-space>
      </a-form-item>
    </a-form>
    <a-modal class="preview" :open="previewVisible" title=" " :footer="null" @cancel="handleCancel">
      <div class="p-15">
        <img alt="example" style="width: 100%" :src="previewImage" />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined, /* QuestionCircleFilled, */ MinusOutlined, CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue'
import { areaCity } from '@/utils/address'
import { validateStr, filterOption, getNowDateTime, getCommonOption, checkPagePermission } from '@/utils/index'
import { VxeTableInstance } from 'vxe-table'
import { GetSupplierInfo, saveDraftApi, ocrRecognize /* , newSupplier */, getAgreement, addAgreement, checkSupplier } from '@/servers/Supplier'
import { GetDeliveryRegionList, GetProductCategoryList } from '@/servers/Common'
import router from '@/router'
import AddressParse from 'address-parse'
// import { Switch } from '@/servers/UmcAuth'
import { useSupplierSettleFormStore } from '@/store/modules/supplierSettlementForm'
import AgreementModal from './components/AgreementModal.vue'

const VITE_APP_ENV = import.meta.env.VITE_APP_ENV

const formStore = useSupplierSettleFormStore()

let uploadUrl = '/api/Files/UploadFile'
let previewUrl = '/api/Files/ViewByFileId'
if (VITE_APP_ENV === 'development') {
  uploadUrl = `/api/api/Files/UploadFile`
  previewUrl = '/api/api/Files/ViewByFileId'
}

const stepList = [
  {
    title: '基本信息',
    icon: 1,
  },
  {
    title: '扩展信息',
    icon: 2,
  },
  {
    title: '跟进信息',
    icon: 3,
  },
  {
    title: '证书上传',
    icon: 4,
  },
  {
    title: '审核结果',
    icon: 5,
  },
]

const contactsTableRef = ref<VxeTableInstance>()
const financeTableRef = ref<VxeTableInstance>()

const userData = ref(JSON.parse(localStorage.getItem('userData') || '{}'))

const originForm = ref()
const step = ref(0)
const loading = ref(false)
const isShowLoadingMask = ref(false)
const isShowMask = ref(false)
const isOriFormSupplierName = ref(false) // 接口获取出来的数据中是否有公司名称
const formRef = ref()
const srs_supplier_finance_infos_item = {
  id: 0,
  account_name: '',
  account_type: null,
  collection_bank: '',
  collection_bank_branch: '',
  collection_card_number: '',
  is_default: null,
}
const srs_supplier_contact_infos_item = {
  id: 0,
  name: '',
  job: '',
  mobile_phone_number: '',
  email: '',
  is_default: null,
}
const form = ref<any>({
  id: 0,
  supplier_name: '',
  credit_id: '',
  credit_code: '',
  company_type: '',
  business_license_validity: '',
  is_long: false,
  business_license_file_id: 0,
  establishment_date: '',
  business_scale: '',
  business_license_province: '',
  business_license_city: '',
  business_license_area: '',
  business_license_address: '',
  office_address_province: '',
  office_address_city: '',
  office_address_area: '',
  office_address_detail: '',
  legal_person_name: '',
  certificate_type: '',
  certificate_number: '',
  id_card_front_file_id: 0,
  id_card_back_file_id: 0,
  supplier_type: '',
  factory_scale: '',
  main_categories: [],
  main_regions: [],
  sku_count: 0,
  main_products: '',
  main_products_min_price: 0,
  main_products_max_price: 0,
  annual_sales: '',
  factory_employee_count: 0,
  settlement_method: '',
  invoice_type: '',
  default_tax_rate: '',
  srs_supplier_contact_infos: [
    {
      id: 0,
      supplier_id: 0,
      name: '',
      job: '',
      mobile_phone_number: '',
      email: '',
      weixin_number: '',
      is_default: true,
    },
  ],
  srs_supplier_finance_infos: [
    {
      id: 0,
      supplier_id: 0,
      account_name: '',
      account_type: '',
      collection_card_number: '',
      collection_bank: '',
      collection_bank_branch: '',
      is_default: true,
    },
  ],
  file_ids: [],
  license_file_ids: [],
  is_audit: false,
  audit_type: 1,
})
const certificateFileList = ref<any>([])
const rules = ref({}) as any
const allRules: Record<string, Rule[]> = {
  fileList: [
    {
      required: true,
      message: '请上传营业执照',
      trigger: 'change',
      validator: () => {
        if (fileList.value.length === 0) {
          return Promise.reject('请上传营业执照')
        }
        return Promise.resolve()
      },
    },
  ],
  credit_id: [
    {
      // required: true,
      // message: '请输入证件编号',
      trigger: 'change',
      validator: () => {
        if (form.value.credit_id.length > 30) {
          return Promise.reject('输入内容不可超过30字符')
        }
        if (form.value.credit_id && !/^[a-zA-Z0-9]+$/.test(form.value.credit_id)) {
          return Promise.reject('仅允许英文和数字')
        }
        return Promise.resolve()
      },
    },
  ],
  credit_code: [
    {
      required: true,
      message: '请输入统一社会信用代码',
    },
  ],
  company_type: [
    {
      required: true,
      message: '请选择公司类型',
    },
  ],
  supplier_type: [
    {
      required: true,
      message: '请选择供应商类型',
    },
  ],
  supplier_name: [
    {
      required: true,
      message: '请输入公司名称',
    },
    {
      trigger: 'change',
      validator: () => {
        if (form.value.supplier_name.length > 50) {
          return Promise.reject('输入内容不可超过50字符')
        }
        return Promise.resolve()
      },
    },
  ],
  business_scale: [
    {
      required: true,
      message: '请选择公司规模',
    },
  ],
  establishment_date: [
    {
      required: true,
      message: '请选择成立日期',
    },
  ],
  business_license_validity: [
    {
      required: true,
      message: '',
      trigger: 'change',
      validator: () => {
        if (!(form.value.business_license_validity || form.value.is_long)) {
          return Promise.reject('请选择营业执照有效期')
        }
        return Promise.resolve()
      },
    },
  ],
  business_license_address: [
    {
      required: true,
      message: '请输入营业执照地址',
      validator: () => {
        if (!form.value.bussinessAddress) {
          return Promise.reject('请输入营业执照地址')
        }
        return Promise.resolve()
      },
    },
  ],
  legal_person_name: [
    {
      required: true,
      message: '请输入法人姓名',
    },
  ],
  certificate_type: [
    {
      required: true,
      message: '请选择证件类型',
    },
  ],
  certificate_number: [
    {
      required: true,
      message: '请输入证件号',
    },
    {
      validator: (_rule, value) => validateStr(_rule, value, 30),
      message: '输入内容不可超过30字符',
    },
  ],
  photoList: [
    {
      required: true,
      message: '请上传法人身份证',
      trigger: 'change',
      validator: () => {
        if (photoList.value.length === 0) {
          return Promise.reject('请上传法人身份证正面')
        }
        if (backPhotoList.value.length === 0) {
          return Promise.reject('请上传法人身份证反面')
        }
        return Promise.resolve()
      },
    },
  ],
  factory_scale: [
    {
      required: true,
      message: '请选择自营/合作工厂规模',
    },
  ],
  main_categories: [
    {
      required: true,
      message: '请选择主营类目',
    },
  ],
  main_regions: [
    {
      required: true,
      message: '请选择主营区域',
    },
  ],
  main_products: [
    { required: true, message: '请输入主营商品', trigger: 'blur' },
    { max: 50, message: '输入内容不可超过50字符', trigger: 'blur' },
  ],
  main_products_min_price: [
    {
      required: true,
      message: '',
    },
    {
      trigger: ['change', 'blur'],
      validator: () => {
        if ((!form.value.main_products_min_price && form.value.main_products_min_price !== 0) || (!form.value.main_products_max_price && form.value.main_products_max_price !== 0)) {
          return Promise.reject('请输入主营商品价格区间')
        }
        if (form.value.main_products_min_price >= form.value.main_products_max_price) {
          return Promise.reject('请输入正确的主营商品价格区间')
        }
        return Promise.resolve()
      },
    },
  ],
  annual_sales: [
    {
      required: true,
      message: '请输入公司年销售额',
    },
  ],
  settlement_method: [
    {
      required: true,
      message: '请选择结算方式',
    },
  ],
  invoice_type: [
    {
      required: true,
      message: '请选择发票类型',
    },
  ],
  default_tax_rate: [
    {
      required: true,
      message: '请选择默认税率',
    },
  ],
  uploadFileList: [
    {
      required: true,
      message: '请上传文件',
      trigger: 'change',
      validator: () => {
        if (uploadFileList.value.length === 0) {
          return Promise.reject('请上传文件')
        }
        return Promise.resolve()
      },
    },
  ],
}
const formRulesKey = [
  [
    'fileList',
    'credit_id',
    'credit_code',
    'company_type',
    'supplier_name',
    'business_scale',
    'establishment_date',
    'business_license_validity',
    'business_license_address',
    'legal_person_name',
    'certificate_type',
    'certificate_number',
    'photoList',
  ],
  ['supplier_type', 'factory_scale', 'main_categories', 'main_regions', 'main_products', 'main_products_min_price', 'annual_sales'],
  ['settlement_method', 'invoice_type', 'default_tax_rate'],
  ['uploadFileList'],
]

const columns = [
  {
    title: '文件',
    dataIndex: 'original_name',
    width: '40%',
  },
  {
    title: '上传人',
    dataIndex: 'account_name',
    width: '20%',
  },
  {
    title: '上传时间',
    dataIndex: 'create_at',
    width: '20%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '20%',
  },
]
const uploadFilesTips = `温馨提示：<br/>
  1. 涉及护肤品商品生产，如：洗护用品，膏霜乳液制品，唇膏、唇彩、眉笔、唇线笔、发蜡，牙膏类商品，清洁类商品等，需上传《化妆品生产许可证》；<br/>
  2. 涉及食品生产，如：粮食加工品、油脂及其制品，调味品，肉制品，乳制品，饮料方便食品，冷冻饮品，速冻食品等，需上传《食品生产许可证》；<br/>

  3. 涉及药品生产，如：化学药剂，中药制剂，生物制品，原材料药及药用辅料，特殊管理药品等，需上传《药品生产许可证》；<br/>
  4. 涉及工业品生产，如：建筑用钢筋，水泥，广播电视传输设备，电线电缆，危险化学品，化肥等，需上传《工业产品生产许可证》；<br/>

  5. 涉及医疗器械相关生产，如：基础耗材，护理用品，诊断监护类，治疗康复类，手术耗材类，医用卫生材料等，需上传《医疗器械生产许可证》；`
const fileList = ref<any>([])
const photoList = ref<any>([])
const backPhotoList = ref<any>([])
const uploadFileList = ref<any>([])
const previewVisible = ref(false)
const previewImage = ref('')
const documentTypeOptions = ref<any>([])
const factoryscaleOptions = ref<any>([]) // 自营/合作工厂规模
const majorTypeOptions = ref<any>([])
const majorAreaOptions = ref<any>([])
const supplierTypeOptions = ref<any>([]) // 供应商类型
// 是否默认
const trueOrFalseOptions = [
  {
    value: true,
    label: '是',
  },
  {
    value: false,
    label: '否',
  },
]

// 账户类型
const accountTypeOptions = ref<any>([])

const companyTypeOptions = ref<any>([]) // 公司类型
const companyModeOptions = ref<any>([]) // 公司规模

const settlementMethodOptions = ref<any>([]) // 结算方式
const invoiceTypeOptions = ref<any>([]) // 发票类型
const defaultTaxRateOptions = ref<any>([]) // 默认税率

// 将省市区数据转换为 a-cascader 所需的格式
const areaOptions = ref(
  areaCity.map((province) => ({
    value: province.name,
    label: province.name,
    children: province.children?.map((city) => ({
      value: city.name,
      label: city.name,
      children: city.children?.map((district) => ({
        value: district.name,
        label: district.name,
      })),
    })),
  })),
)

const validators = ({ row, isRequired, length, column, type }: any) => {
  const value = row[column.field]
  const tipsBefore = type == 'select' ? '请选择' : '请输入'
  if (typeof value != 'number' && typeof value != 'boolean' && isRequired && (!value || value.trim() === '')) {
    return Promise.reject(tipsBefore + column.title)
  }
  if (length && String(value).length > length) {
    return Promise.reject(`不可超过${length}字符`)
  }
  return Promise.resolve()
}

const validateEmail = ({ row, length, column }: any) => {
  const value = row[column.field]
  if (!value) {
    return Promise.resolve()
  }
  if (length && String(value).length > length) {
    return Promise.reject(`不可超过${length}字符`)
  }
  const emailRegex = /^\S+@\S+\.\S+$/
  if (!emailRegex.test(value)) {
    return Promise.reject('请输入正确的邮箱格式')
  }
  return Promise.resolve()
}

const readFile = async (id) => {
  const url = `${previewUrl}?fileId=${id}`
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })
    console.log('response', response)
    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    /*     const contentDisposition = response.headers.get('Content-Disposition')
    let fileName = 'unknown_file'
    if (contentDisposition) {
      const match = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (match != null && match[1]) {
        fileName = match[1].replace(/['"]/g, '')
      }
    }
    console.log('解析出文件名称', fileName) */

    // 可选：一段时间后释放内存
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    return { url: previewUrl /* , name: fileName */ }
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}

const beforeUpload = (file, uploadFileList, list, type) => {
  console.log('beforeUpload=============', file, uploadFileList, '=========')
  let check = true
  // eslint-disable-next-line no-async-promise-executor
  const promise = new Promise(async (resolve, reject) => {
    const arr = ['image/png', 'image/jpeg', 'image/jpg']
    if ([4].includes(type)) {
      arr.push('application/pdf')
    }
    if (file.name.split('.').pop()?.toLowerCase() == 'jfif') {
      check = false
    }
    let tips = '支持文件格式：PNG，JPEG，JPG'
    if (arr.indexOf(file.type) == -1 || !check) {
      if ([4].includes(type)) {
        tips += '，PDF'
      }
      message.error(tips)
      check = false
    }
    if (file.size / 1024 / 1024 > 10) {
      message.error('文件大小不得大于10M')
      check = false
    }
    if (check) {
      const formData = new FormData()
      formData.append('files', file) // 'file' 是后端接收文件的字段名
      formData.append('fileModule', 'Supplier')
      formData.append('account_id', userData.value.id)
      formData.append('company_id', userData.value.company_id)
      const response = await fetch(`${uploadUrl}`, {
        method: 'POST',
        body: formData,
        headers: {
          logintoken: userData.value.login_token,
        },
      })
      if (!response.ok) {
        throw new Error(`上传失败: ${response.status}`)
      }
      const res = await response.json()
      if (res.success == true) {
        file.id = res.data[0].id
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          const tempFile = {
            original_name: file.name,
            uid: Date.now().toString(),
            status: 'done',
            url: reader.result as string,
            uploadTime: getNowDateTime().formattedDateTime,
            id: res.data[0].id,
            create_at: res.data[0].create_at,
            account_name: res.data[0].account_name,
          }
          list.push(tempFile)
        }
        if (type < 4) {
          getOcrRecognize(type, file)
        }
      }
      // eslint-disable-next-line no-promise-executor-return
      return reject()
    }
  })

  // console.log('file', tableobj.value.fileList)

  return promise
}

const isValidDateFormat = (dateStr) => {
  // 匹配格式：YYYY年MM月DD日
  const regex = /^(\d{4})年(\d{1,2})月(\d{1,2})日$/
  return regex.test(dateStr)
}

const getOcrRecognize = async (type, file) => {
  const params = {} as any
  switch (type) {
    case 1: // 营业执照
      params.type = 2
      break
    case 2: // 身份证正面
      params.type = 1
      params.side = 1
      break
    case 3: // 身份证反面
      params.type = 1
      params.side = 2
      break
    default:
      break
  }
  const data = (await ocrRecognize(params, { file })).data
  if (type == 1) {
    // 营业执照
    form.value.is_long = false // 重置勾选
    form.value.legal_person_name = data.legal_representative || ''
    if (userData.value?.company !== '西月集团' && !userData.value?.company_id) {
      form.value.supplier_name = data.company_name || ''
    }
    form.value.credit_code = data.registration_number || ''
    form.value.business_license_address = data.address || ''
    if (isValidDateFormat(data.establishment_date)) {
      form.value.establishment_date = data.establishment_date
    } else {
      form.value.establishment_date = ''
    }
    if (['不约定期限', '长期', '永久'].includes(data.valid_period)) {
      form.value.business_license_validity = ''
      form.value.is_long = true
    } else if (isValidDateFormat(data.valid_period)) {
      form.value.business_license_validity = data.valid_period
    } else {
      // 会有返回'无'的情况，除此之外不知是否还有别的
      form.value.business_license_validity = ''
    }
    if (data.address) {
      const list = AddressParse.parse(data.address, true)
      console.log('AddressParse', list)
      if (list.length) {
        const { province, city, area, details } = list[0]
        if (province && city && area) {
          form.value.bussinessAddress = [province, city, area]
        } else {
          form.value.bussinessAddress = []
        }
        form.value.business_license_address = details || ''
      }
    }
    form.value.company_type = companyTypeOptions.value.find((n) => n.label == data.company_type_short)?.label
    checkSupplier({ creditcode: form.value.credit_code })
  } else if (type == 2) {
    // 身份证正面
    if (form.value.certificate_type === 0) {
      form.value.certificate_number = data.id_number || ''
      form.value.legal_person_name = data.name || ''
    }
  }
}

const ToPagePreviewbyId = async (id) => {
  const url = `${previewUrl}?fileId=${id}`
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })
    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)
    // 在新窗口打开预览
    window.open(previewUrl, '_blank')
    // 可选：一段时间后释放内存
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    return previewUrl
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}

const handleCancel = () => {
  previewVisible.value = false
}

const handlePreview = async (file: any) => {
  // console.log('handlePreview-file', file)
  // await ToPagebyId(file.id)
  if (!file.url && !file.preview) {
    file.preview = (await getBase64(file.originFileObj)) as string
  }
  previewImage.value = file.url || file.preview
  previewVisible.value = true
}

function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}

/* const resetForm = () => {
  formRef.value.resetFields()
  form.value = {}
  form.value.bussinessAddress = []
  form.value.officeAddress = []
  fileList.value = []
  photoList.value = []
  backPhotoList.value = []
} */

/* const onClickCreateCompany = async () => {
  isShowLoadingMask.value = true
  try {
    const res = await newSupplier()
    if (res.success == true) {
      const switchRes = await Switch({ id: res.data })
      if (res.success && res.data) {
        const newUserData = {
          ...switchRes.data,
          login_time: Math.floor(Date.now() / 1000),
        }
        // 更新localStorage
        localStorage.setItem('userData', JSON.stringify(newUserData))
        isShowLoadingMask.value = false
        window.location.reload()
      }
    }
  } catch (error) {
    console.log('新增企业失败', error)
    isShowLoadingMask.value = false
  }
} */

const onClickBack = async () => {
  if (loading.value) {
    return
  }
  try {
    if (JSON.stringify(form.value) != JSON.stringify(originForm.value)) {
      loading.value = true
      const params = handleFormData(0)
      await saveDraftApi(params)
      loading.value = false
    }
  } catch (err) {
    console.log('保存草稿失败', err)
    loading.value = false
  }
  step.value--
}

const handleFormData = (type: number) => {
  if (form.value.bussinessAddress) {
    form.value.business_license_province = form.value.bussinessAddress[0]
    form.value.business_license_city = form.value.bussinessAddress[1]
    form.value.business_license_area = form.value.bussinessAddress[2]
  }
  if (form.value.officeAddress) {
    form.value.office_address_province = form.value.officeAddress[0]
    form.value.office_address_city = form.value.officeAddress[1]
    form.value.office_address_area = form.value.officeAddress[2]
  } else {
    form.value.office_address_province = ''
    form.value.office_address_city = ''
    form.value.office_address_area = ''
  }
  form.value.business_license_file_id = fileList.value[fileList.value.length - 1]?.id
  form.value.id_card_front_file_id = photoList.value[photoList.value.length - 1]?.id
  form.value.id_card_back_file_id = backPhotoList.value[backPhotoList.value.length - 1]?.id
  form.value.license_file_ids = certificateFileList.value.map((n) => {
    return n.id
  })
  form.value.is_audit = type === 1
  form.value.audit_type = 1
  return form.value
}

const onSubmit = async (type: number) => {
  // type: 1提交
  try {
    await formRef.value.validateFields()
    try {
      if (loading.value) {
        return
      }
      loading.value = true
      const params = handleFormData(type)
      await saveDraftApi(params)
      loading.value = false
      step.value++
      await getSupplierInfo()
      resetFormChangeStatus()
      formStore.setFormChange(false)
    } catch (err) {
      console.log('提交失败', err)
      loading.value = false
    }
  } catch (error: any) {
    console.log('表单校验失败', error)

    const firstErrorField = error.errorFields[0]?.name[0]
    if (firstErrorField) {
      const inputElement = document.getElementById(`form_item_${firstErrorField}`) as HTMLInputElement
      if (inputElement) {
        inputElement.focus()
      }
    }
  }
}

const onSaveDraft = async () => {
  try {
    try {
      if (!form.value.supplier_name) {
        message.info('请输入公司名称')
        return
      }
      if (loading.value) {
        return
      }
      loading.value = true
      const params = handleFormData(0)
      await saveDraftApi(params)
      loading.value = false
      message.success('保存草稿成功')
      resetFormChangeStatus()
    } catch (err) {
      console.log('提交失败', err)
      loading.value = false
    }
  } catch (error: any) {
    console.log('表单校验失败', error)

    const firstErrorField = error.errorFields[0]?.name[0]
    if (firstErrorField) {
      const inputElement = document.getElementById(`form_item_${firstErrorField}`) as HTMLInputElement
      if (inputElement) {
        inputElement.focus()
      }
    }
  }
}

const onJumpDetail = () => {
  router.push({
    path: '/companyInfo',
  })
}

const reWrite = ref(false)
const onJumpFirst = () => {
  step.value = 0
  reWrite.value = true
}

const changeContactDefault = (val, index, tableData) => {
  if (val) {
    form.value[tableData].forEach((item, i) => {
      if (index !== i) {
        item.is_default = false
      }
    })
  }
}

const onAddTableItem = (tableData) => {
  if (form.value[tableData].length < 5) form.value[tableData].push({})
}

const onDelContact = (index, tableData) => {
  if (form.value[tableData].length <= 1) return
  form.value[tableData].splice(index, 1)
}

const onDelFile = (index) => {
  certificateFileList.value.splice(index, 1)
}

// const downloadLocalFile = (file) => {
//   console.log('row file', file)

//   const link = document.createElement('a')
//   // 如果是本地文件，使用 file.originFileObj 获取原始文件
//   if (file.originFileObj) {
//     link.href = URL.createObjectURL(file.originFileObj)
//     link.download = file.name
//   } else if (file.url) {
//     // 如果是 data URL 格式
//     link.href = file.url
//     link.download = file.name || 'image'
//   }
//   link.click()
//   // 释放 object URL
//   if (file.originFileObj) {
//     URL.revokeObjectURL(link.href)
//   }
// }

const getOptions = async () => {
  const [companytype_list, businessscale_list, certificatetype_list, settlementmethod_list, invoicetype_list, defaulttaxrate_list, publicAccountTypeList, factoryscale_list, supplierType] =
    await getCommonOption([5, 10, 4, 1, 8, 6, 3, 7, 20])
  companyTypeOptions.value = companytype_list
  companyModeOptions.value = businessscale_list
  documentTypeOptions.value = certificatetype_list
  settlementMethodOptions.value = settlementmethod_list
  invoiceTypeOptions.value = invoicetype_list
  defaultTaxRateOptions.value = defaulttaxrate_list
  accountTypeOptions.value = publicAccountTypeList
  factoryscaleOptions.value = factoryscale_list
  supplierTypeOptions.value = supplierType

  GetProductCategoryList({ page: 1, pageSize: 9999 }).then((res) => {
    majorTypeOptions.value = res.data?.list.map((n) => {
      return {
        value: n.id,
        label: n.name,
      }
    })
  })

  GetDeliveryRegionList({ page: 1, pageSize: 9999 }).then((res) => {
    majorAreaOptions.value = res.data?.list.map((n) => {
      return {
        value: n.id,
        label: n.name,
      }
    })
  })
}

const getSupplierInfo = () => {
  return new Promise((resolve, reject) => {
    ;(async () => {
      try {
        const res = await GetSupplierInfo()
        form.value = res.data
        isOriFormSupplierName.value = !!form.value.supplier_name // 获取的供应商详情中是否有供应商名称
        form.value.supplier_name = form.value.supplier_name || userData.value?.company || ''
        if (form.value.is_long) {
          form.value.business_license_validity = ''
        }
        if (form.value.establishment_date == '0001-01-01 00:00:00') {
          form.value.establishment_date = ''
        }
        if (form.value.business_license_validity == '0001-01-01 00:00:00') {
          form.value.business_license_validity = ''
        }
        if (!form.value.company_type) {
          form.value.company_type = ''
        }
        if (!form.value.supplier_type) {
          form.value.supplier_type = ''
        }
        if (form.value.business_license_province && form.value.business_license_city && form.value.business_license_area) {
          form.value.bussinessAddress = [form.value.business_license_province, form.value.business_license_city, form.value.business_license_area]
        }
        if (form.value.office_address_province && form.value.office_address_city && form.value.office_address_area) {
          form.value.officeAddress = [form.value.office_address_province, form.value.office_address_city, form.value.office_address_area]
        }
        certificateFileList.value = form.value.license_files
        if (form.value.audit_status > 10 && !reWrite.value) {
          // 非草稿状态即已提交审核，直接展示最后一页
          step.value = 4
          reWrite.value = false
        }
        if (form.value.settlement_method === 0 && settlementMethodOptions.value.length) {
          form.value.settlement_method = settlementMethodOptions.value[0].value
        }
        if (form.value.business_license_file_id) {
          const fileInfo = await readFile(form.value.business_license_file_id)
          fileList.value = [
            {
              uid: 1,
              url: fileInfo.url,
              // name: fileInfo.name,
              status: 'done',
              id: form.value.business_license_file_id,
            },
          ]
        }
        if (form.value.id_card_front_file_id) {
          const fileInfo = await readFile(form.value.id_card_front_file_id)
          photoList.value = [
            {
              uid: 2,
              url: fileInfo.url,
              // name: fileInfo.name,
              status: 'done',
              id: form.value.id_card_front_file_id,
            },
          ]
        }
        if (form.value.id_card_back_file_id) {
          const fileInfo = await readFile(form.value.id_card_back_file_id)
          backPhotoList.value = [
            {
              uid: 3,
              url: fileInfo.url,
              // name: fileInfo.name,
              status: 'done',
              id: form.value.id_card_back_file_id,
            },
          ]
        }
        if (form.value.license_files?.length) {
          form.value.uploadFileList = form.value.license_files.map((n) => {
            return {
              uid: n.id,
              url: n.url,
              status: 'done',
              id: n.id,
              name: n.name,
            }
          })
          uploadFileList.value = form.value.uploadFileList
        }
        resolve(1)
      } catch (err) {
        console.log('getSupplierInfo-err', err, userData.value?.company)
        if ((err as any)?.code === -2) {
          // 没有供应商信息,不需要显示报错
          form.value.supplier_name = userData.value?.company || ''
        }
        formStore.setFormChange(false)
        reject(err)
      }
    })()
  })
}

const changeOfficeAddressDetail = () => {
  if (!form.value.officeAddress?.length) {
    message.info('请先选择省市区')

    nextTick(() => {
      form.value.office_address_detail = ''
    })
  }
}

// 处理协议确认
const handleAgreementConfirm = async () => {
  await addAgreement()
  isShowMask.value = false
}

// 显示协议弹窗
const showAgreement = () => {
  isShowMask.value = true
}

// 重置表单变化状态
const resetFormChangeStatus = () => {
  originForm.value = JSON.parse(JSON.stringify(form.value))
  formStore.setForm(form.value)
  formStore.setOriginForm(originForm.value)
}

const setRules = () => {
  rules.value = {}
  formRulesKey[step.value]?.forEach((key) => {
    rules.value[key] = allRules[key]
  })
}

watch(
  () => form.value.srs_supplier_contact_infos,
  () => {
    if (step.value < 2) {
      return
    }

    if (step.value === 2) {
      if (!form.value.srs_supplier_contact_infos.length) {
        form.value.srs_supplier_contact_infos.push(srs_supplier_contact_infos_item)
      }
    }
    if (form.value.srs_supplier_contact_infos.length == 1 || !form.value.srs_supplier_contact_infos.find((n) => n.is_default)) {
      form.value.srs_supplier_contact_infos[0].is_default = true
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => form.value.srs_supplier_finance_infos,
  () => {
    if (step.value < 2) {
      return
    }
    if (step.value === 2) {
      if (!form.value.srs_supplier_finance_infos.length) {
        form.value.srs_supplier_finance_infos.push(srs_supplier_finance_infos_item)
      }
    }
    if (form.value.srs_supplier_finance_infos.length == 1 || !form.value.srs_supplier_finance_infos.find((n) => n.is_default)) {
      form.value.srs_supplier_finance_infos[0].is_default = true
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => step.value,
  async () => {
    if (step.value < 4) {
      try {
        await getSupplierInfo()
        setRules()
      } catch (err) {
        setRules()
      }
    }
    resetFormChangeStatus()
  },
  { immediate: true },
)

watch(
  () => form.value.is_long,
  (val) => {
    if (val) {
      delete rules.value.business_license_validity
      delete form.value.business_license_validity
    } else {
      rules.value.business_license_validity = allRules.business_license_validity
    }
  },
)

watch(
  () => form.value,
  (val) => {
    formStore.setForm(val)
  },
  { deep: true },
)

onMounted(async () => {
  getOptions()
  const isAgreeRes = await getAgreement()
  if (!isAgreeRes.data) {
    showAgreement()
  }
})
</script>

<style lang="scss" scoped>
.main {
  position: relative;
  height: calc(100% - 24px) !important;
  overflow-y: scroll;
  color: #333;
}

:deep(.ant-steps) {
  padding: 1rem 6.25rem !important;
}

:deep(.ant-steps-icon) {
  display: inline-block;
  width: 3rem !important;
  height: 3rem !important;
  padding: 0 !important;
  font-weight: bold;
  line-height: 3rem !important;
  color: white !important;
  background: orangered;
  border-radius: 100%;
}

:deep(.ant-steps-item-wait .ant-steps-icon) {
  background: #b9b9b9 !important;
}

.title {
  padding: 8px;
  margin: 20px !important;
  font-weight: bold;
  background-color: #e7f2ff !important;
}

.input-width {
  width: 31.25rem;
}

.short {
  width: 23rem;
}

.price-before :deep(.ant-input-number-group-addon) {
  background-color: #fff;
  border-radius: 0 !important;
}

.price-affter :deep(.ant-input-number) {
  border-left: none;
  border-radius: 0 !important;
}

.grey {
  color: grey;
}

.vxe-table-box {
  padding: 0 1.7rem;
  margin-bottom: 2rem;
}

.w100 {
  width: 100%;
}

.rule-table :deep(.ant-form-item) {
  margin: 0;
}

.red {
  color: red;
}

.strong {
  font-weight: bold;
}

.font-xl {
  font-size: 1.875rem;
}

.font-l {
  font-size: 1.5rem;
}

.pd20 {
  padding: 20px;
}

.color333 {
  color: #333;
}

.green {
  color: rgb(22 207 121);
}

.origin {
  color: orange;
}

.icon {
  font-size: 100px;
}

.uploadBox {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  .uploadBoxtext {
    margin-left: 20px;
    font-size: 10px;
    color: #666;
  }
}

.richtextbox {
  font-size: 10px;
  color: #666;
}

.mask {
  position: absolute;
  inset: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(87 87 87 / 80.1%);
}
</style>
